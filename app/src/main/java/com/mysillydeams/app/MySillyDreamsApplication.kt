package com.mysillydeams.app

import android.app.Application
import android.util.Log
import com.google.android.gms.common.ConnectionResult
import com.google.android.gms.common.GoogleApiAvailability

/**
 * Application class for MySillyDreams
 * Handles app-wide initialization and configuration
 */
class MySillyDreamsApplication : Application() {

    override fun onCreate() {
        super.onCreate()
        Log.d("MySillyDreamsApp", "Application starting...")
        
        // Check Google Play Services availability
        checkGooglePlayServices()
    }

    private fun checkGooglePlayServices() {
        val googleApiAvailability = GoogleApiAvailability.getInstance()
        val resultCode = googleApiAvailability.isGooglePlayServicesAvailable(this)
        
        when (resultCode) {
            ConnectionResult.SUCCESS -> {
                Log.d("MySillyDreamsApp", "Google Play Services is available and up to date")
            }
            ConnectionResult.SERVICE_MISSING -> {
                Log.e("MySillyDreamsApp", "Google Play Services is missing")
            }
            ConnectionResult.SERVICE_VERSION_UPDATE_REQUIRED -> {
                Log.w("MySillyDreamsApp", "Google Play Services needs to be updated")
            }
            ConnectionResult.SERVICE_DISABLED -> {
                Log.e("MySillyDreamsApp", "Google Play Services is disabled")
            }
            else -> {
                Log.e("MySillyDreamsApp", "Google Play Services error: $resultCode")
            }
        }
    }
}
