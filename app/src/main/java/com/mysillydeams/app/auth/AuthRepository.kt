package com.mysillydeams.app.auth

import android.content.Context
import android.os.CancellationSignal
import android.util.Log
import androidx.credentials.ClearCredentialStateRequest
import androidx.credentials.Credential
import androidx.credentials.CredentialManager
import androidx.credentials.CredentialManagerCallback
import androidx.credentials.CustomCredential
import androidx.credentials.GetCredentialRequest
import androidx.credentials.GetCredentialResponse
import androidx.credentials.exceptions.ClearCredentialException
import androidx.credentials.exceptions.GetCredentialException
import androidx.credentials.exceptions.GetCredentialProviderConfigurationException
import androidx.credentials.exceptions.NoCredentialException
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.google.android.gms.auth.api.signin.GoogleSignInClient
import com.google.android.gms.auth.api.signin.GoogleSignInOptions
import com.google.android.gms.common.ConnectionResult
import com.google.android.gms.common.GoogleApiAvailability
import com.google.android.libraries.identity.googleid.GetGoogleIdOption
import com.google.android.libraries.identity.googleid.GoogleIdTokenCredential
import com.google.android.libraries.identity.googleid.GoogleIdTokenParsingException
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseUser
import com.google.firebase.auth.GoogleAuthProvider
import com.mysillydeams.app.config.AppConfig
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.tasks.await
import java.util.concurrent.Executors

class AuthRepository(private val context: Context) {

    private val auth: FirebaseAuth = FirebaseAuth.getInstance()
    private val credentialManager: CredentialManager = CredentialManager.create(context)

    // Legacy Google Sign-In client (fallback for emulators)
    private val googleSignInClient: GoogleSignInClient by lazy {
        val gso = GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
            .requestIdToken(AppConfig.googleWebClientId)
            .requestEmail()
            .build()
        GoogleSignIn.getClient(context, gso)
    }

    private val _authState = MutableStateFlow<AuthState>(AuthState.Loading)
    val authState: StateFlow<AuthState> = _authState.asStateFlow()

    private val _currentUser = MutableStateFlow<FirebaseUser?>(null)
    val currentUser: StateFlow<FirebaseUser?> = _currentUser.asStateFlow()
    
    init {
        // Check if user is already signed in
        _currentUser.value = auth.currentUser
        _authState.value = if (auth.currentUser != null) {
            AuthState.Authenticated
        } else {
            AuthState.Unauthenticated
        }
        
        // Listen for auth state changes
        auth.addAuthStateListener { firebaseAuth ->
            val user = firebaseAuth.currentUser
            _currentUser.value = user
            _authState.value = if (user != null) {
                AuthState.Authenticated
            } else {
                AuthState.Unauthenticated
            }
        }
    }
    
    private fun checkGooglePlayServices(): Boolean {
        val googleApiAvailability = GoogleApiAvailability.getInstance()
        val resultCode = googleApiAvailability.isGooglePlayServicesAvailable(context)

        return when (resultCode) {
            ConnectionResult.SUCCESS -> {
                Log.d("AuthRepository", "Google Play Services is available")
                true
            }
            else -> {
                Log.e("AuthRepository", "Google Play Services not available: $resultCode")
                false
            }
        }
    }

    suspend fun signInWithGoogle(): Result<Unit> {
        return try {
            Log.d("AuthRepository", "Starting Google Sign-In")

            // Check Google Play Services availability first
            if (!checkGooglePlayServices()) {
                val errorMsg = "Google Play Services is not available on this device. Please install or update Google Play Services."
                Log.e("AuthRepository", errorMsg)
                _authState.value = AuthState.Error(errorMsg)
                return Result.failure(Exception(errorMsg))
            }

            // Validate configuration on first access
            AppConfig.validateConfiguration()
            Log.d("AuthRepository", "Configuration validated")

            _authState.value = AuthState.Loading

            // Create Google ID option with more explicit configuration
            val googleIdOption = GetGoogleIdOption.Builder()
                .setServerClientId(AppConfig.googleWebClientId)
                .setFilterByAuthorizedAccounts(false)
                .setAutoSelectEnabled(true)
                .build()

            Log.d("AuthRepository", "Google ID option created with client ID: ${AppConfig.googleWebClientId}")

            // Create credential request
            val request = GetCredentialRequest.Builder()
                .addCredentialOption(googleIdOption)
                .build()

            Log.d("AuthRepository", "Credential request created")

            // Get credential using Credential Manager
            val result = credentialManager.getCredential(
                request = request,
                context = context
            )

            Log.d("AuthRepository", "Credential received, processing...")
            handleSignInResult(result.credential)
            Result.success(Unit)
        } catch (e: GetCredentialProviderConfigurationException) {
            Log.w("AuthRepository", "Credential Manager not available, falling back to legacy Google Sign-In", e)
            return signInWithGoogleLegacy()
        } catch (e: NoCredentialException) {
            val errorMsg = "No Google accounts available. Please add a Google account to your device."
            Log.e("AuthRepository", errorMsg, e)
            _authState.value = AuthState.Error(errorMsg)
            Result.failure(e)
        } catch (e: GetCredentialException) {
            val errorMsg = "Sign in failed: ${e.message}"
            Log.e("AuthRepository", errorMsg, e)
            _authState.value = AuthState.Error(errorMsg)
            Result.failure(e)
        } catch (e: Exception) {
            val errorMsg = "Unexpected error: ${e.message}"
            Log.e("AuthRepository", errorMsg, e)
            _authState.value = AuthState.Error(errorMsg)
            Result.failure(e)
        }
    }

    private suspend fun signInWithGoogleLegacy(): Result<Unit> {
        return try {
            Log.d("AuthRepository", "Using legacy Google Sign-In")

            // For now, return an error asking user to use a physical device
            val errorMsg = "Google Sign-In requires a physical device or an emulator with Google Play Store. " +
                    "Please test on a physical device or use an emulator with Google Play Store installed."
            Log.e("AuthRepository", errorMsg)
            _authState.value = AuthState.Error(errorMsg)
            Result.failure(Exception(errorMsg))
        } catch (e: Exception) {
            val errorMsg = "Legacy Google Sign-In failed: ${e.message}"
            Log.e("AuthRepository", errorMsg, e)
            _authState.value = AuthState.Error(errorMsg)
            Result.failure(e)
        }
    }

    private suspend fun handleSignInResult(credential: Credential): Result<FirebaseUser> {
        return try {
            Log.d("AuthRepository", "Processing credential of type: ${credential.type}")

            // Check if credential is of type Google ID
            if (credential is CustomCredential && credential.type == GoogleIdTokenCredential.TYPE_GOOGLE_ID_TOKEN_CREDENTIAL) {
                Log.d("AuthRepository", "Valid Google ID credential received")

                // Create Google ID Token
                val googleIdTokenCredential = GoogleIdTokenCredential.createFrom(credential.data)
                val idToken = googleIdTokenCredential.idToken

                Log.d("AuthRepository", "ID token extracted, signing in to Firebase")

                // Sign in to Firebase using the token
                val firebaseCredential = GoogleAuthProvider.getCredential(idToken, null)
                val authResult = auth.signInWithCredential(firebaseCredential).await()
                val user = authResult.user

                if (user != null) {
                    Log.d("AuthRepository", "Firebase authentication successful for user: ${user.email}")
                    _authState.value = AuthState.Authenticated
                    Result.success(user)
                } else {
                    Log.e("AuthRepository", "Firebase authentication failed - no user returned")
                    _authState.value = AuthState.Error("Authentication failed")
                    Result.failure(Exception("Authentication failed"))
                }
            } else {
                Log.e("AuthRepository", "Invalid credential type: ${credential.type}")
                _authState.value = AuthState.Error("Invalid credential type")
                Result.failure(Exception("Credential is not of type Google ID"))
            }
        } catch (e: GoogleIdTokenParsingException) {
            _authState.value = AuthState.Error("Failed to parse Google ID token")
            Result.failure(e)
        } catch (e: Exception) {
            _authState.value = AuthState.Error(e.message ?: "Authentication failed")
            Result.failure(e)
        }
    }
    
    fun signOut() {
        // Firebase sign out
        auth.signOut()

        // Clear credential state from all credential providers
        val clearRequest = ClearCredentialStateRequest()
        credentialManager.clearCredentialStateAsync(
            clearRequest,
            CancellationSignal(),
            Executors.newSingleThreadExecutor(),
            object : CredentialManagerCallback<Void?, ClearCredentialException> {
                override fun onResult(result: Void?) {
                    _authState.value = AuthState.Unauthenticated
                }

                override fun onError(e: ClearCredentialException) {
                    // Even if clearing fails, we still signed out from Firebase
                    _authState.value = AuthState.Unauthenticated
                }
            }
        )
    }
    
    fun getCurrentUser(): FirebaseUser? = auth.currentUser
}

sealed class AuthState {
    object Loading : AuthState()
    object Authenticated : AuthState()
    object Unauthenticated : AuthState()
    data class Error(val message: String) : AuthState()
}
